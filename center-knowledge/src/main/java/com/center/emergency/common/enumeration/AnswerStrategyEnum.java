package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AnswerStrategyEnum implements IEnumerate<String> {
    ONLY_KB("ONLY_KB","1"),
    KB_FIRST_MODEL("KB_FIRST_MODEL","2"),
    ONLY_QA("ONLY_QA","3");

    private String value;
    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
