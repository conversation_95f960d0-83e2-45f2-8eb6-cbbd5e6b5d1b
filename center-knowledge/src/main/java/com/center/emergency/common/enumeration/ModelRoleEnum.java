package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用于表示模型在对应模型组中的身份
 * @date 2025/05/26 18:12
 */
@AllArgsConstructor
public enum ModelRoleEnum implements IEnumerate<String> {
    ADAPTIVE("ADAPTIVE", "自适应"),
    DEEP_THINK("DEEP_THINK", "深度思考"),
    NORMAL("NORMAL", "普通");

    private String value;
    private String description;

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }
}
