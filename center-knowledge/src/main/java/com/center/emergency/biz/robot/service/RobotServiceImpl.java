package com.center.emergency.biz.robot.service;

import cn.hutool.core.util.StrUtil;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.pojo.KnowledgeBaseResp;
import com.center.emergency.biz.robot.DTO.KnowledgeBaseDTO;
import com.center.emergency.biz.robot.persitence.*;
import com.center.emergency.biz.robot.pojo.*;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.center.infrastructure.system.biz.depart.persistence.QDepartModel;
import com.center.infrastructure.system.biz.depart.pojo.DepartAndKbListResp;
import com.center.infrastructure.system.biz.depart.service.DepartServiceImpl;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RobotServiceImpl implements RobotService {

    @Autowired
    private RobotRepository robotRepository;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Autowired
    private RobotKBRepository robotKBRepository;

    @Resource
    private DepartRepository departRepository;

    @Resource
    private DepartServiceImpl departServiceImpl;

    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;

    private static final String pathSeparator = "/";


    @Override
    @Transactional
    public Long createRobots(RobotCreateReq req) {
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        //1.检查机器人名称是否重复
        if (robotRepository.existsByRobotNameAndTenantId(req.getRobotName(), tenantId)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "机器人名称重复");
        }
        //2.创建机器人并设置默认值
        RobotModel robotModel = OrikaUtils.convert(req, RobotModel.class);

        // 特殊处理 dialogueExamples
        if (CollectionUtils.isNotEmpty(req.getDialogueExamples())) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                String dialogueJson = objectMapper.writeValueAsString(req.getDialogueExamples());
                robotModel.setDialogueExamples(dialogueJson);
            } catch (JsonProcessingException e) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "对话示例转换失败");
            }
        }

        if (req.getSimilarityThreshold()==null){
            robotModel.setSimilarityThreshold(BigDecimal.valueOf(0.80));
        }
        if (req.getMaxHits()==null){
            robotModel.setMaxHits(3);
        }

        //3. 保存实体并获取生成的id
        RobotModel saveRobot = robotRepository.save(robotModel);
        Long robotId = saveRobot.getId();

        //4. 创建关联知识库
        List<Long> knowledgeIdsList = req.getKnowledgeBaseIds();

        if (!knowledgeIdsList.isEmpty()) {
            List<RobotKnowledgeModel> newEntries = knowledgeIdsList.stream()
                    .map(kbId -> {
                        RobotKnowledgeModel robotKnowledgeModel = new RobotKnowledgeModel();
                        robotKnowledgeModel.setRobotId(robotId);
                        robotKnowledgeModel.setKbId(kbId);
                        robotKnowledgeModel.setTenantId(LoginContextHolder.getLoginUserTenantId());
                        return robotKnowledgeModel;
                    })
                    .collect(Collectors.toList());
            robotKBRepository.saveAll(newEntries); // 批量插入
        } else {
            log.error("机器人关联知识库列表为空");
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "机器人关联知识库不能为空");
        }
        return robotId;
    }

    @Override    //分页查询所有机器人方法
    public PageResult<RobotPageResp> getRobotsByPage(RobotPageReq pageReq) {
        Pageable pageable = PageRequest.of(pageReq.getPageNo() - 1, pageReq.getPageSize());  // 创建分页请求，page从0开始
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        QRobotModel qRobotModel = QRobotModel.robotModel;
        QUserModel qUserModel = QUserModel.userModel;

        BooleanBuilder whereBuilder = new BooleanBuilder();
        whereBuilder.and(qRobotModel.tenantId.eq(tenantId));

        //2. robotName 不为空时，根据名称进行模糊查询；否则全部查询
        if (StrUtil.isNotEmpty(pageReq.getName())) {
            whereBuilder.and(qRobotModel.robotName.contains(pageReq.getName()));
        }

        // 2.1 获取查询结果
        JPQLQuery jpqlQuery = jpaQueryFactory.select((Projections.bean(
                        RobotPageResp.class,
                        qRobotModel.id,
                        qRobotModel.robotName,
                        qRobotModel.robotStatus,
                        qRobotModel.createTime,
                        qRobotModel.remark,
                        qUserModel.displayName)))
                .from(qRobotModel)
                .join(qUserModel).on(qRobotModel.updaterId.eq(qUserModel.id))
                .where(whereBuilder)
                .orderBy(qRobotModel.createTime.desc()) // 按 createTime 倒序排列
                .offset(pageable.getOffset()) // 使用 pageable 的偏移量
                .limit(pageable.getPageSize()); // 设置每页记录数

        Long total = jpqlQuery.fetchCount();
        List<RobotPageResp> resultList = jpqlQuery.fetch();

        // 2.2 构建分页响应并返回
        return PageResult.of(resultList, total);
    }


    @Override
    @Transactional
    public void saveRobot(RobotSaveReq req) {
        // 1. 根据ID查找机器人
        Long robotId = req.getId();
        String robotName = robotRepository.findById(robotId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在")).getRobotName();

        //1.1 判断更新的机器人名称是否发生变化，如果变化则判重并更新；否则不判重
        if (!robotName.equals(req.getRobotName()) && robotRepository.existsByRobotNameAndTenantId(req.getRobotName(), LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "机器人名称重复");
        }

        // 2. 更新机器人表数据
        RobotModel updateRobot = OrikaUtils.convert(req, RobotModel.class);
        // 特殊处理 dialogueExamples
        if (CollectionUtils.isNotEmpty(req.getDialogueExamples())) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                String dialogueJson = objectMapper.writeValueAsString(req.getDialogueExamples());
                updateRobot.setDialogueExamples(dialogueJson);
            } catch (JsonProcessingException e) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "对话示例转换失败");
            }
        }

        // 3. 保存机器人更新后的数据
        robotRepository.save(updateRobot);

        // 4. 根据传入的知识库id列表修改 机器人-知识库关联表的信息
        List<Long> knowledgeIds = req.getKnowledgeBaseIds();
        if (knowledgeIds != null && !knowledgeIds.isEmpty()) {

            // 4.1 当前robot_id删除多余的 kb_id
            robotKBRepository.deleteByRobotId(robotId);

            // 4.2 插入缺失的 kb_id (批量插入)
            List<RobotKnowledgeModel> newEntries = knowledgeIds.stream()
                    .map(kbId -> {
                        RobotKnowledgeModel robotKnowledgeModel = new RobotKnowledgeModel();
                        robotKnowledgeModel.setRobotId(robotId);
                        robotKnowledgeModel.setKbId(kbId);
                        return robotKnowledgeModel;
                    })
                    .collect(Collectors.toList());
            robotKBRepository.saveAll(newEntries); // 批量插入
        } else {
            log.error("机器人关联知识库列表为空");
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "机器人信息更新失败");
        }
    }


    @Override
    public void switchStatus(RobotStatusReq statusReq) {
        // 1. 根据id查找机器人
        RobotModel robot = robotRepository.findById(statusReq.getId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在"));
        // 2. 切换状态
        robot.setRobotStatus(CommonStatusEnum.valueOf(statusReq.getRobotStatus()));
        // 3. 保存更改
        robotRepository.save(robot);
    }

    @Override
    public RobotPreviewResp previewRobots(Long id) {

        RobotModel robotModel = robotRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在"));

        //1. 设置返回参数
        RobotPreviewResp previewResp = OrikaUtils.convert(robotModel, RobotPreviewResp.class);
        /*previewResp.setWelcomeMessage(robotModel.getWelcomeMessage());
        previewResp.setRemark(robotModel.getRemark());
        previewResp.setRobotName(robotModel.getRobotName());
        previewResp.setId(id);*/

        //2. 通过机器人Id从center_robot_knowledge_bases表中查询kb_id，再根据kb_id从center_knowledge_bases表查询knowledge_name
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;
        QRobotModel qRobotModel = QRobotModel.robotModel;

        //3. 关联知识库信息
        List<KnowledgeBaseDTO> knowledgeBaseDTOs = jpaQueryFactory
                .select(Projections.bean(
                        KnowledgeBaseDTO.class,
                        qKnowledgeBaseModel.id,
                        qKnowledgeBaseModel.kbName))
                .from(qRobotModel)
                .join(qRobotKnowledgeModel)
                .on(qRobotModel.id.eq(qRobotKnowledgeModel.robotId)
                        .and(qRobotModel.tenantId.eq(qRobotKnowledgeModel.tenantId)))
                .join(qKnowledgeBaseModel)
                .on(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id)
                        .and(qKnowledgeBaseModel.tenantId.eq(qRobotKnowledgeModel.tenantId)))
                .where(qRobotModel.id.eq(id))
                .fetch();  // 确保使用 fetch() 方法

        List<Long> knowledgeBaseIds = new ArrayList<>();
        List<String> knowledgeBaseNames = new ArrayList<>();
        for (KnowledgeBaseDTO knowledgeBaseDTO : knowledgeBaseDTOs) {
            knowledgeBaseIds.add(knowledgeBaseDTO.getId());
            knowledgeBaseNames.add(knowledgeBaseDTO.getKbName());
        }

        //3. 将查询到的knowledge_name列表放入返回体中
        previewResp.setKnowledgeBaseId(knowledgeBaseIds);
        previewResp.setKnowledgeBaseName(knowledgeBaseNames);

        return previewResp;
    }

    /**
     * 获取部门及其知识库列表
     * <p>
     * 此方法用于获取指定部门及其关联知识库的列表信息它首先获取当前登录用户的部门和租户ID，
     * 然后调用服务层方法获取初始部门列表随后，根据部门ID是否在特定检查列表中，决定是获取用户所在部门的知识库列表，
     * 还是指定部门的知识库列表最后，将知识库列表合并到结果中并返回
     *
     * @param departId 部门ID，用于指定需要获取信息的部门
     * @param path     路径信息
     * @return 返回一个包含部门及其知识库信息的列表
     */
    @Override
    public List<DepartAndKbListResp> listDepartAndKb(Long departId, String path) {
        // 获取当前登录用户的部门ID
        Long userDepartId = LoginContextHolder.getLoginUserDepartId();
        if (userDepartId == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "获取登录用户部门ID失败");
        }
        // 获取当前登录用户的租户ID
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        if (tenantId == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "获取登录用户租户ID失败");
        }
        // 调用服务层方法，获取部门列表信息
        List<DepartAndKbListResp> result = departServiceImpl.listDeparts(departId, path, true);
        //如果departId不为空
        if(departId!=null){
            result.addAll(listKbs(departId));
        }

        // 返回合并后的结果列表
        return result;
    }


    /**
     * 根据部门ID获取知识库列表
     * 此方法首先从数据库中查询与部门ID关联的所有知识库模型，
     * 然后构建并返回一个包含这些知识库的树形结构列表
     *
     * @param departId 部门ID，用于查询知识库
     * @return 返回一个构建好的知识库树形结构列表
     */
    public List<DepartAndKbListResp> listKbs(Long departId) {

        // 构建并返回知识库树形结构列表
        List<KnowledgeBaseModel>  knowledgeBaseModelList = knowledgeBaseRepository.findByDepartmentId(departId);
        return buildKbTree(knowledgeBaseModelList);
    }

    /**
     * 构建知识库树结构
     * 此方法的目的是将给定的知识库模型列表转换为树形结构，以便于在前端展示或进一步处理
     * 它通过创建一个映射来跟踪每个知识库模型，并将其关系构建为树形结构
     *
     * @param knowledgeBaseModelList 知识库模型列表，用于构建树形结构如果列表为空或为null，方法将返回一个空列表
     * @return 返回一个列表，包含构建好的知识库树结构
     */
    private List<DepartAndKbListResp> buildKbTree(List<KnowledgeBaseModel> knowledgeBaseModelList) {
        List<DepartAndKbListResp> result = new ArrayList<>();
        if (null != knowledgeBaseModelList) {
            for (KnowledgeBaseModel knowledgeBaseModel : knowledgeBaseModelList) {
                DepartAndKbListResp departAndKbListResp =
                        new DepartAndKbListResp(knowledgeBaseModel.getId(), knowledgeBaseModel.getKbName(), null, false, Collections.emptyList());
                result.add(departAndKbListResp);
            }
        }
        return result;
    }
    @Override
    public RobotDetailsResp getRobotDetails(Long id) {
        // 获取机器人信息
        RobotModel robotModel = robotRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,
                        String.format("机器人不存在,请刷新页面后重试")));
        // 转换为返回对象
        RobotDetailsResp robotDetailsResp = OrikaUtils.convert(robotModel, RobotDetailsResp.class);

        // 联查知识库信息及部门信息
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;
        QDepartModel qDepartModel = QDepartModel.departModel;

        // 查询关联知识库及部门信息
        List<KnowledgeBaseResp> knowledgeBases = jpaQueryFactory
                .select(Projections.bean(
                        KnowledgeBaseResp.class,
                        qKnowledgeBaseModel.id.as("id"),
                        qKnowledgeBaseModel.kbName.as("kbName"),
                        qDepartModel.id.as("departmentId"),
                        qDepartModel.departName.as("departmentName"),
                        qKnowledgeBaseModel.aiFilebId.as("aiFilebId"),
                        qKnowledgeBaseModel.aiFaqbId.as("aiFaqbId"),
                        qKnowledgeBaseModel.createTime.as("createTime"),
                        qKnowledgeBaseModel.updateTime.as("updateTime")
                ))
                .from(qRobotKnowledgeModel)
                .join(qKnowledgeBaseModel).on(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .join(qDepartModel).on(qKnowledgeBaseModel.departmentId.eq(qDepartModel.id))
                .where(qRobotKnowledgeModel.robotId.eq(id))
                .fetch();

        // 设置知识库信息
        robotDetailsResp.setKnowledgeBases(knowledgeBases.isEmpty() ? Collections.emptyList() : knowledgeBases);


        return robotDetailsResp;
    }


}
