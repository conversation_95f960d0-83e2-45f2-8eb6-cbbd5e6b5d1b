package com.center.emergency.biz.chat.common.parser;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.center.emergency.biz.chat.common.enumeration.ParsedEventType;
import com.center.emergency.biz.chat.common.persitence.ParsedEvent;
import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.service.ChatServiceImpl;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;

@Slf4j
public class ChatSseParser implements StreamParser {

    private static final String CHAT_DEFAULT_ERROR_MESSAGE     = "系统正忙，请稍后再试。";
    private static final String CHAT_DEFAULT_SPAMERROR_MESSAGE = "我们的平台致力于提供一个安全、尊重的环境，某些话题可能不适宜讨论。我会很乐意帮助您解答其他问题，谢谢您的理解和配合。";

    private final ChatVO          chatVO;
    private final SseEmitter      emitter;
    private final ChatServiceImpl chatService;
    private final AtomicBoolean   completed;        // ★ 线程安全
    private final StringBuilder   builder;

    public ChatSseParser(SseEmitter emitter,
                         ChatServiceImpl chatService,
                         boolean initCompleted,
                         ChatVO chatVO,
                         StringBuilder builder) {

        this.emitter      = emitter;
        this.chatService  = chatService;
        this.chatVO       = chatVO;
        this.builder      = builder;
        this.completed    = new AtomicBoolean(initCompleted);
    }

    /* -------------------- 入口 -------------------- */

    @Override
    public ParsedEvent parse(String rawData, String eventType) throws Exception {
        ParsedEvent event = new ParsedEvent();
        event.setRawData(rawData);
        event.setOriginalEventType(eventType);   // 保留服务端原事件名
        // 统一解析成 JSON
        JSONObject json = JSONUtil.parseObj(rawData);
        log.info("原始数据：{}", rawData);
        /* ---------- ① 处理服务端显式 completed ---------- */
        String serverEvent = json.getStr("event");
        if ("completed".equalsIgnoreCase(serverEvent)) {
            event.setEventType(ParsedEventType.END);   // 让 Listener 走 END 分支
            event.setContent(null);                    // END 不携带正文
            return event;
        }
        /* ---------- ② 正常 message 流 ---------- */
        if ("200".equals(json.getStr("code"))) {
            return parseMessage(json, event);          // 提取 response 字段
        }
        /* ---------- ③ 其它情况统一抛给上层做 error ---------- */
        log.info("解析到非 200 码数据：{}", rawData);
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED);
    }


    /* -------------------- 私有实现 -------------------- */

    /** 处理 message 数据 */
    private ParsedEvent parseMessage(JSONObject json, ParsedEvent event) {
        event.setEventType(ParsedEventType.MESSAGE);

        String content = json.getStr("response");
        if (StringUtils.isBlank(content)) {   // ★ 改为 isBlank，空串直接丢弃
            return event;                     //   不再返回 MESSAGE
        }

        builder.append(content);

        /* ---------- 流式敏感词检查 ---------- */
        if (builder.length() >= 20 &&
                (builder.length() % 3 == 0 || builder.length() >= 200) &&
                chatService.checkAnswerSpam(builder.toString())) {

            // 标记为 spam，让 Listener 作统一处理
            event.setEventType(ParsedEventType.SPAM);
            event.setContent(CHAT_DEFAULT_SPAMERROR_MESSAGE);
            return event;
        }

        event.setContent(content);
        return event;
    }

    /* -------------------- error / spam 工具 -------------------- */

    /** 供 Listener 收尾兜底使用 */
    @Override
    public void handleSpamResponse(String message) {
        if (completed.get()) {
            log.warn("已完成的 SSE 再次 spam 结束，忽略");
            return;
        }
        try {
            chatService.saveQuestionAndAnswerAndSession(chatVO, builder.toString(), false);
            emitter.send(SseEmitter.event().name("spam").data(message));
            emitter.send(SseEmitter.event().name("end").data(CommonResult.success(chatVO)));
        } catch (IOException ioe) {
            log.error("spam 事件写出失败", ioe);
            handleException("spam 事件写出失败：" + ioe.getMessage());
        } finally {
            safeComplete();
        }
    }

    private void handleException(String trace) {
        if (completed.get()) {
            log.warn("已完成的 SSE 再次发送 error，忽略");
            return;
        }
        try {
            emitter.send(SseEmitter.event()
                    .name("error")
                    .data(CommonResult.error(
                            OUTER_SERVER_EXCEPTION.getCode(),
                            CHAT_DEFAULT_ERROR_MESSAGE,
                            trace)));
        } catch (IOException ioe) {
            log.error("发送 error 事件失败", ioe);
        } finally {
            safeComplete();
        }
    }

    /** 幂等 complete */
    private void safeComplete() {
        if (completed.compareAndSet(false, true)) {
            try { emitter.complete(); } catch (Exception ignore) {}
        }
    }
}
