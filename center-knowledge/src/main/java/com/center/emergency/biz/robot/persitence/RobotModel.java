package com.center.emergency.biz.robot.persitence;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

@Data
@Entity
@QueryEntity
@Table(name = "center_robots")
public class RobotModel extends BaseTenantModel {

    @Column(name = "robotName",nullable = false, unique = true)
    private String robotName;

    // 机器人欢迎语
    @Column(name = "welcomeMessage")
    private String welcomeMessage;

    // 机器人备注
    @Column(name = "remark")
    private String remark;

    //机器人状态
    @Column(name = "status")
    @Enumerated(value = EnumType.STRING)
    private CommonStatusEnum robotStatus;

    // 机器人关联的模型Id
    @Column(name = "model_id")
    private Long modelId;

    // 机器人系统提示词
    @Column(name = "system_prompt")
    private String systemPrompt;

    // 机器人对话示例
    @Column(name = "dialogue_examples")
    private String dialogueExamples;

    // 检索模式：vector/text/hybrid
    @Column(name = "search_mode")
    @Enumerated(value = EnumType.STRING)
    private SearchModeEnum searchMode;

    // 问答策略：only_kb/kb_first_model
    @Column(name = "answer_strategy")
    @Enumerated(value = EnumType.STRING)
    private AnswerStrategyEnum answerStrategy;

    // 文本匹配相似度阈值
    @Column(name = "similarity_threshold", precision = 3, scale = 2)
    private BigDecimal similarityThreshold;

    // 最大召回数量
    @Column(name = "max_hits")
    private Integer maxHits;

}
