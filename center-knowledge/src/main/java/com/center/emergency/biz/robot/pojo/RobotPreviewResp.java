package com.center.emergency.biz.robot.pojo;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RobotPreviewResp extends RobotBase {

    @Schema(description = "机器人ID")
    private Long id;
    // 关联知识库
    @Schema(description = "关联知识库",example = "")
    private List<String> knowledgeBaseName;

    //关联知识库id
    @Schema(description = "关联知识库id",example = "2")
    private List<Long> knowledgeBaseId;

    // 系统提示词
    @Schema(description = "机器人系统提示词", example = "你是一个专业的医疗咨询机器人")
    private String systemPrompt;

    // 对话示例
    @Schema(description = "机器人对话示例", example = "用户：你好 机器人：您好，有什么我可以帮您的吗？")
    private String dialogueExamples;

    // 检索模式
    @Schema(description = "检索模式：VECTOR / TEXT / HYBRID", example = "HYBRID")
    private SearchModeEnum searchMode;

    // 问答策略
    @Schema(description = "问答策略：ONLY_KB / KB_FIRST_MODEL / ONLY_QA", example = "KB_FIRST_MODEL")
    private AnswerStrategyEnum answerStrategy;

    // 文本匹配相似度阈值
    @Schema(description = "文本匹配相似度阈值", example = "0.80")
    private BigDecimal similarityThreshold;

    // 最大召回数量
    @Schema(description = "最大召回数量", example = "3")
    private Integer maxHits;

}
