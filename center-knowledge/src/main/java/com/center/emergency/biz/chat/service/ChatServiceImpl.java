package com.center.emergency.biz.chat.service;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.center.emergency.biz.chat.common.listener.ChatSSEListener;
import com.center.emergency.biz.chat.common.parser.ChatSseParser;
import com.center.emergency.biz.chat.common.parser.StreamParser;
import com.center.emergency.biz.chat.persistence.*;
import com.center.emergency.biz.chat.pojo.*;
import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.files.persistence.QFileTagModel;
import com.center.emergency.biz.knowledge.persistence.QKnowledgeModel;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.biz.robot.persitence.RobotModel;
import com.center.emergency.biz.robot.persitence.RobotRepository;
import com.center.emergency.biz.robot.pojo.RobotPreviewResp;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.emergency.common.enumeration.AutoGenerationTaskTypeEnum;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.emergency.common.utils.AntispamUtil;
import com.center.emergency.common.utils.OkHttpUtils;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.depart.persistence.DepartModel;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSources;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CodingErrorAction;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;

@Service
@Slf4j
public class ChatServiceImpl implements ChatService {

    @Resource
    AntispamUtil antispamUtil;

    @Resource
    Snowflake snowflake;

    @Resource
    private Executor seeExecutor;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;


    @Value("${python.api-base-url}")
    private String modelUrl;

    @Value("${python.chat-api-base-url}")
    private String chatBaseUrl;


    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private RobotRepository robotRepository;

    @Resource
    private ChatQuestionRepository chatQuestionRepository;

    @Resource
    private ChatAnswerRepository chatAnswerRepository;

    @Resource
    private ChatSessionRepository chatSessionRepository;

    @Resource
    private DepartRepository departRepository;

    private static String CHAT_DEFAULT_ERROR_MESSAGE = "系统正忙，请稍后再试。";


    @Override
    public SseEmitter chatWithRobot(ChatWithRobotReq chatWithRobotReq, Boolean isSimulate) {
        Long modelId = checkRobot(chatWithRobotReq.getRobotId());
        List<Map<String, String>> tags = getTagsFromRobot(chatWithRobotReq.getRobotId());
        Set<String> kbIds = new HashSet<>();
        List<String> fileIds = new ArrayList<>();
        buildFromRobot(chatWithRobotReq.getRobotId(), kbIds, fileIds);

        ChatVO chatVO = OrikaUtils.convert(chatWithRobotReq, ChatVO.class);
        if (!isSimulate) {
            chatVO.setQuestionId(snowflake.nextId());
            chatVO.setAnswerId(snowflake.nextId());
            if (chatWithRobotReq.getSessionId() == null) {
                chatVO.setSessionId(snowflake.nextId());
            }
        }

        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
        if (checkQuestionSpam(chatVO.getQuestion(), chatVO, isSimulate, emitter)) {
            return emitter;
        }

        try {
            emitter.send(SseEmitter.event().name("header").data(CommonResult.success(chatVO)));
        } catch (IOException e) {
            log.error("流式对话出错", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
            return emitter;
        }

        // 构建算法模型请求参数
        HashMap<String, Object> param = newBuildRequestParam(chatVO, kbIds, fileIds, tags, isSimulate, modelId);
        // 执行异步SSE请求
        chatRunAsync(emitter, chatVO, modelUrl + "/api/local_doc_qa/local_doc_chat", param, isSimulate);

        return emitter;
    }

    @Override
    public String autoGeneration(AutoGenerationReq autoGenerationReq) {
        try {
            // 构建请求参数
            HashMap<String, Object> param = new HashMap<>();
            param.put("name", autoGenerationReq.getRobotName());
            param.put("description", autoGenerationReq.getDescription());
            param.put("task_type", autoGenerationReq.getTaskType().getDescription());

            JsonNode rootNode = callPromptApi(param);

            String result = null;
            if (autoGenerationReq.getTaskType().equals(AutoGenerationTaskTypeEnum.OPENING_REMARK)) {
                result = rootNode.path("welcome_message").asText();
            } else if (autoGenerationReq.getTaskType().equals(AutoGenerationTaskTypeEnum.SYSTEM_PROMPT)) {
                result = rootNode.path("system_prompt").asText();
            }

            return result;
        } catch (Exception e) {
            log.error("智能生成失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "智能生成失败，请稍后重试。");
        }
    }

    @Override
    public List<String> autoGenerateQuickQuestions(QuickQuestionsReq quickQuestionsReq) {
        try {
            // 构建请求参数
            HashMap<String, Object> param = new HashMap<>();
            param.put("name", quickQuestionsReq.getRobotName());
            param.put("description", quickQuestionsReq.getDescription());
            param.put("task_type", "对话示例");

            JsonNode rootNode = callPromptApi(param);

            // 取出 quick_questions 数组
            JsonNode quickQuestionsNode = rootNode.path("quick_questions");

            List<String> resultList = new ArrayList<>();
            quickQuestionsNode.forEach(item -> resultList.add(item.asText()));

            return resultList;
        } catch (Exception e) {
            log.error("智能生成失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "智能生成失败，请稍后重试。");
        }
    }

    private JsonNode callPromptApi(Map<String, Object> param) throws IOException {
        OkHttpClient client = OkHttpUtils.getOkHttpClient();
        Request request = OkHttpUtils.getJsonPostRequest(modelUrl + "/api/local_doc_qa/generate_custom_prompt", param);

        Response response = client.newCall(request).execute();
        String responseBody = response.body().string();
        log.info("算法返回结果：{}", responseBody);

        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(responseBody);

        int code = rootNode.path("code").asInt();
        if (code != 200) {
            String msg = rootNode.path("msg").asText("生成失败，请稍后重试。");
            log.warn("算法接口返回非200，msg: {}", msg);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "算法调用失败");
        }

        return rootNode;
    }


    @Override
    @Transactional
    public SseEmitter chatWithKnowledge(ChatWithKnowledgeReq chatWithKnowledgeReq) {
        List<Map<String, String>> tags = getTagsFromKnowledgeBase(chatWithKnowledgeReq.getKnowledgeBaseId());
        Set<String> kbIds = new HashSet<>();
        List<String> fileIds = new ArrayList<>();
        buildFromKnowledgeBase(chatWithKnowledgeReq.getKnowledgeBaseId(), kbIds, fileIds);
        ChatVO chatVO = OrikaUtils.convert(chatWithKnowledgeReq, ChatVO.class);

        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
        if (checkQuestionSpam(chatVO.getQuestion(), chatVO, Boolean.TRUE, emitter)) {
            return emitter;
        }

        try {
            emitter.send(SseEmitter.event().name("header").data(CommonResult.success(chatVO)));
        } catch (IOException e) {
            log.error("流式对话出错", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
            return emitter;
        }

        // 构建算法模型请求参数
        HashMap<String, Object> param = buildRequestParam(chatVO, kbIds, fileIds, tags, Boolean.TRUE);
        // 执行异步SSE请求
        chatRunAsync(emitter, chatVO, modelUrl + "/api/local_doc_qa/local_doc_chat", param, Boolean.TRUE);

        return emitter;
    }

    /**
     * 异步执行SSE请求的方法。
     * 该方法通过异步线程调用外部系统的SSE接口并处理返回的结果，同时向客户端推送实时数据。
     *
     * @param emitter SseEmitter对象，用于推送SSE事件到客户端。
     * @param chatVO  当前会话的ChatVO对象，包含相关的会话信息。
     * @param url     请求的外部系统接口URL。
     * @param param   请求参数，包含调用外部接口所需的参数。
     */
    /**
     * 发起异步 SSE 请求并把服务端流推送给前端。
     *
     * @param emitter     Spring SseEmitter
     * @param chatVO      业务上下文
     * @param url         目标接口
     * @param param       POST JSON 参数
     * @param isSimulate  是否模拟模式
     */
    private void chatRunAsync(SseEmitter emitter,
                              ChatVO      chatVO,
                              String      url,
                              HashMap<String, Object> param,
                              Boolean     isSimulate) {

        /* ---------- ① 共享资源准备 ---------- */
        // 用于流式累积答案 & 敏感词校验
        StringBuilder answerBuilder = new StringBuilder();

        // 线程安全 completed 标志由 ChatSseParser 内部持有
        StreamParser parser =
                new ChatSseParser(emitter, this, false, chatVO, answerBuilder);

        // 用来通知主任务“流已结束”
        CountDownLatch latch = new CountDownLatch(1);

        /* ---------- ② 真正的异步任务 ---------- */
        CompletableFuture.runAsync(() -> {

            // 创建业务 Listener（共享 parser / builder / latch）
            ChatSSEListener listener = new ChatSSEListener(
                    parser, latch, emitter, this, chatVO, isSimulate, answerBuilder);

            try {
                OkHttpClient client  = OkHttpUtils.getOkHttpClient();
                Request      request = OkHttpUtils.getJsonPostRequest(url, param);

                // 启动 SSE；OkHttp 回调线程将调用 listener
                EventSource.Factory factory = EventSources.createFactory(client);
                factory.newEventSource(request, listener);

                // ⚠️ 阻塞等待流结束，释放线程池请按需调整
                latch.await();

            } catch (Exception e) {
                log.error("请求 SSE 发生异常", e);
                try {
                    emitter.send(
                            SseEmitter.event()
                                    .name("error")
                                    .data(CommonResult.error(
                                            OUTER_SERVER_EXCEPTION.getCode(),
                                            CHAT_DEFAULT_ERROR_MESSAGE,
                                            e.getMessage())));
                } catch (IOException ex) {
                    log.error("发送 error 事件失败", ex);
                }
            }
        }, seeExecutor);
    }


    private HashMap<String, Object> buildRequestParam(ChatVO chatVO, Set<String> kbIds, List<String> fileIds, List<Map<String, String>> tags, Boolean isSimulate) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("tags_list", tags);
        param.put("kb_ids", kbIds);
        param.put("file_ids", fileIds);
        param.put("question", chatVO.getQuestion());
        param.put("user_token", String.valueOf(LoginContextHolder.getLoginUserId()));
        param.put("user_id", "zyx");
        param.put("streaming", 1);
        param.put("history", getHistory(chatVO.getSessionId(), chatVO.getQuestionId(), isSimulate));
        param.put("hybrid_search", 0);
        param.put("rerank", 1);
        param.put("only_need_search_results", 0);
        param.put("temperature", 0.3);
        param.put("top_p", 1.0);
        param.put("top_k", 30);

        return param;
    }

    private HashMap<String, Object> newBuildRequestParam(ChatVO chatVO, Set<String> kbIds, List<String> fileIds, List<Map<String, String>> tags, Boolean isSimulate, Long modelId) {
        QRobotModel qRobotModel = QRobotModel.robotModel;
        QLargeModel qLargeModel = QLargeModel.largeModel;

        RobotModelDTO robotModelDTO;
        if (modelId != null) {
            robotModelDTO = jpaQueryFactory.select(
                            Projections.bean(
                                    RobotModelDTO.class,
                                    qLargeModel.baseUrl.as("apiBase"),
                                    qLargeModel.apiKey,
                                    qLargeModel.modelName.as("model"),
                                    qLargeModel.maxTokens.as("maxToken"),
                                    qLargeModel.inputContextLength,
                                    qRobotModel.searchMode,
                                    qRobotModel.answerStrategy.as("answerMode"),
                                    qRobotModel.similarityThreshold.as("scoreThreshold"),
                                    qLargeModel.temperature,
                                    qLargeModel.topP,
                                    qRobotModel.maxHits.as("topK"),
                                    qRobotModel.systemPrompt.as("customPrompt")
                            ))
                    .from(qRobotModel)
                    .leftJoin(qLargeModel).on(qRobotModel.modelId.eq(qLargeModel.id))
                    .where(qRobotModel.id.eq(chatVO.getRobotId()))
                    .fetchOne();
        }else {
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(qRobotModel.id.eq(chatVO.getRobotId()));
            builder.and(qLargeModel.sourceType.eq(SourceTypeEnum.SYSTEM));
            robotModelDTO = jpaQueryFactory.select(
                            Projections.bean(
                                    RobotModelDTO.class,
                                    qLargeModel.baseUrl.as("apiBase"),
                                    qLargeModel.apiKey,
                                    qLargeModel.modelName.as("model"),
                                    qLargeModel.maxTokens.as("maxToken"),
                                    qLargeModel.inputContextLength,
                                    qRobotModel.searchMode,
                                    qRobotModel.answerStrategy.as("answerMode"),
                                    qRobotModel.similarityThreshold.as("scoreThreshold"),
                                    qLargeModel.temperature,
                                    qLargeModel.topP,
                                    qRobotModel.maxHits.as("topK"),
                                    qRobotModel.systemPrompt.as("customPrompt")
                            ))
                    .from(qRobotModel,qLargeModel)
                    .where(builder)
                    .fetchOne();
        }

        HashMap<String, Object> param = new HashMap<>();
        // 安全地添加非空参数方法
        BiConsumer<String, Object> putIfNotNull = (key, value) -> {
            if (value != null) {
                param.put(key, value);
            }
        };

        param.put("kb_ids", kbIds);
        param.put("file_ids", fileIds);
        param.put("question", chatVO.getQuestion());
        param.put("user_id", "zyx");
        param.put("history", getHistory(chatVO.getSessionId(), chatVO.getQuestionId(), isSimulate));
        if (robotModelDTO.getSearchMode() != null) {
            param.put("search_mode", Integer.valueOf(robotModelDTO.getSearchMode().getDescription()));
        }
        if (robotModelDTO.getAnswerMode() != null) {
            param.put("answer_mode", Integer.valueOf(robotModelDTO.getAnswerMode().getDescription()));
        }
        putIfNotNull.accept("top_k", robotModelDTO.getTopK());
        putIfNotNull.accept("custom_prompt", StringUtils.isNotBlank(robotModelDTO.getCustomPrompt()) ? robotModelDTO.getCustomPrompt() : null);
        putIfNotNull.accept("score_threshold", robotModelDTO.getScoreThreshold());
        param.put("api_base", robotModelDTO.getApiBase());
        param.put("api_key", robotModelDTO.getApiKey());
        param.put("model", robotModelDTO.getModel());
        putIfNotNull.accept("max_token", robotModelDTO.getMaxToken());
        putIfNotNull.accept("api_context_length", robotModelDTO.getInputContextLength());
        putIfNotNull.accept("temperature", robotModelDTO.getTemperature());
        putIfNotNull.accept("top_p", robotModelDTO.getTopP());

        log.info("传给算法的参数{}", param);
        return param;
    }

    @Override
    @Transactional
    public SseEmitter reAnswer(Long answerId) {
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;
        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qChatAnswerModel.id.eq(answerId));
        builder.and(qChatAnswerModel.sessionId.eq(qChatSessionModel.id));
        builder.and(qChatAnswerModel.questionId.eq(qChatQuestionModel.id));
        Tuple tuple =
                queryFactory
                        .select(
                                qChatAnswerModel.id,
                                qChatQuestionModel.id,
                                qChatQuestionModel.content,
                                qChatSessionModel.robotId,
                                qChatSessionModel.id)
                        .from(qChatAnswerModel, qChatSessionModel, qChatQuestionModel)
                        .where(builder)
                        .fetchFirst();

        if (null != tuple) {
            ChatVO chatVO = new ChatVO();
            chatVO.setAnswerId(tuple.get(qChatAnswerModel.id));
            chatVO.setQuestionId(tuple.get(qChatQuestionModel.id));
            chatVO.setQuestion(tuple.get(qChatQuestionModel.content));
            chatVO.setRobotId(tuple.get(qChatSessionModel.robotId));
            chatVO.setSessionId(tuple.get(qChatSessionModel.id));
            Set kbids = new HashSet();
            List fileds = new ArrayList();
            buildFromRobot(chatVO.getRobotId(), kbids, fileds);

            SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
            if (checkQuestionSpam(chatVO.getQuestion(), chatVO, Boolean.TRUE, emitter)) {
                return emitter;
            }

            try {
                emitter.send(SseEmitter.event().name("header").data(CommonResult.success(chatVO)));
            } catch (IOException e) {
                log.error("流式对话出错", e);
                handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
                return emitter;
            }

            // 构建算法模型请求参数
            Long modelId = checkRobot(chatVO.getRobotId());
            HashMap<String, Object> param = newBuildRequestParam(chatVO, kbids, fileds, getTagsFromRobot(chatVO.getRobotId()), Boolean.FALSE, modelId);
            // 执行异步SSE请求
            chatRunAsync(emitter, chatVO, modelUrl + "/api/local_doc_qa/local_doc_chat", param, Boolean.FALSE);
            return emitter;
        } else {
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "回答不存在！", answerId);
        }
    }


    @Override
    public List<RobotPreviewResp> searchAllRobot() {
        List<RobotModel> robotModelList = robotRepository.findByTenantIdAndRobotStatus(LoginContextHolder.getLoginUserTenantId()
                , CommonStatusEnum.ACTIVE);
        List<RobotPreviewResp> result = OrikaUtils.convertList(robotModelList, RobotPreviewResp.class);
        return result;
    }

    @Override
    public List<ChatSessionResp> searchAllSession(Long robotId) {
        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qChatSessionModel.robotId.eq(robotId));
        builder.and(qChatSessionModel.creatorId.eq(LoginContextHolder.getLoginUserId()));
        List<ChatSessionResp> result = queryFactory.select(Projections.bean(ChatSessionResp.class
                        , qChatSessionModel.id
                        , qChatSessionModel.title))
                .from(qChatSessionModel)
                .where(builder)
                .orderBy(qChatSessionModel.createTime.desc())
                .limit(10)
                .fetch();

        return result;
    }

    @Override
    public void thumbsUp(Long id) {
        updateAnswerThumbs(id, Boolean.TRUE);
    }

    @Override
    public void thumbsDown(Long id) {
        updateAnswerThumbs(id, Boolean.FALSE);
    }

    @Override
    public List<ChatHistoryResp> searchSessionHistory(Long sessionId) {
        List<ChatQuestionModel> questionModelList = chatQuestionRepository.findBySessionIdOrderByCreateTimeAsc(sessionId);
        List<ChatAnswerModel> answerModelList = chatAnswerRepository.findBySessionIdOrderByCreateTimeAsc(sessionId);
        List<ChatHistoryResp> result = new ArrayList();
        Map<Long, ChatQuestionModel> chatQuestionModelMap = new HashMap<>();
        Iterator<ChatQuestionModel> iterator = questionModelList.iterator();
        while (iterator.hasNext()) {
            ChatQuestionModel chatQuestionModel = iterator.next();
            chatQuestionModelMap.put(chatQuestionModel.getId(), chatQuestionModel);
        }
        Iterator<ChatAnswerModel> answerModelIterator = answerModelList.iterator();
        while (answerModelIterator.hasNext()) {
            ChatAnswerModel chatAnswerModel = answerModelIterator.next();
            ChatHistoryResp chatHistoryResp = new ChatHistoryResp();
            result.add(chatHistoryResp);
            chatHistoryResp.setChatAnswerBase(OrikaUtils.convert(chatAnswerModel, ChatAnswerBase.class));
            chatHistoryResp.setChatQuestionBase(OrikaUtils.convert(chatQuestionModelMap.get(chatAnswerModel.getQuestionId())
                    , ChatQuestionBase.class));
        }
        return result;
    }

    private SseEmitter streamQuestion(ChatVO chatVO, Boolean isSimulate, Set<String> kbIds, List<String> fileIds, List<Map<String, String>> tags) {
        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
        if (checkQuestionSpam(chatVO.getQuestion(), chatVO, isSimulate, emitter)) {
            return emitter;
        }

        try {
            emitter.send(SseEmitter.event().name("header").data(CommonResult.success(chatVO)));
        } catch (IOException e) {
            log.error("流式对话出错", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
            return emitter;
        }


        HashMap<String, Object> param = new HashMap<>();
        param.put("tags_list", tags);
        param.put("kb_ids", kbIds);
        param.put("file_ids", fileIds);
        param.put("question", chatVO.getQuestion());
        param.put("user_token", String.valueOf(LoginContextHolder.getLoginUserId()));
        param.put("user_id", "zyx");
        param.put("streaming", 1);
        param.put("history", getHistory(chatVO.getSessionId(), chatVO.getQuestionId(), isSimulate));
//        param.put("api_base",chatBaseUrl);
//        param.put("api_key","xxx");
//        param.put("model","qwen2-72b");
//        param.put("max_token",512);
//        param.put("api_context_length",8192);
        param.put("hybrid_search", 0);
        param.put("rerank", 1);
        param.put("only_need_search_results", 0);
        param.put("temperature", 0.5);
        param.put("top_p", 0.99);
        param.put("top_k", 20);

        OkHttpClient client = OkHttpUtils.getOkHttpClient();
        Request request = OkHttpUtils.getJsonPostRequest(modelUrl + "/api/local_doc_qa/local_doc_chat", param);
        Call call = client.newCall(request);

        seeExecutor.execute(
                () -> {
                    try (Response response = call.execute()) {
                        StringBuilder builder = new StringBuilder();
                        // 判断模型接口是否调用成功
                        ResponseBody body = response.body();
                        CharsetDecoder decoder = Charset.forName("UTF-8").newDecoder();
                        decoder.onMalformedInput(CodingErrorAction.IGNORE); // 忽略错误，也可以选择报告或替换

                        try (InputStream inputStream = Objects.requireNonNull(body).byteStream();
                             Reader reader = new InputStreamReader(inputStream, decoder)) {

                            char[] buffer = new char[4000];
                            int charsRead;

                            while ((charsRead = reader.read(buffer)) != -1) {
                                String chunk = new String(buffer, 0, charsRead);
                                if (StringUtils.isEmpty(chunk) || chunk.equals("data: [DONE]\n\n")) {
                                    log.info("接收完成");
                                    break;
                                }
                                try {
                                    JSONObject jsonObject =
                                            JSONUtil.toBean(chunk.substring("data:".length()), JSONObject.class);
                                    String content = jsonObject.getByPath("response", String.class);
                                    if (StringUtils.isNotEmpty(content) && null != emitter) {
                                        builder.append(content);
                                        // 当模型返回的内容到达一定长度后 （比如20），需要对后后面的内容进行敏感词检查
                                        //                                从性能的角度考虑，可以每增加3~5个字检查一次。
                                        int answerLength = builder.length();
                                        if (answerLength > 20 && answerLength % 3 == 0) {
                                            if (checkQuestionSpam(snowflake.nextIdStr(), builder.toString())) {
                                                saveQuestionAndAnswerAndSession(chatVO, builder.toString(), isSimulate);
                                                handleSpamResponse(
                                                        emitter,
                                                        "\"我们的平台致力于提供一个安全、尊重的环境，某些话题可能不适宜讨论。我会很乐意帮助您解答其他问题，谢谢您的理解和配合。\"");
                                                return;
                                            }
                                        }
                                        emitter.send(Base64.getEncoder().encodeToString(content.getBytes(StandardCharsets.UTF_8))
                                                , MediaType.TEXT_PLAIN);
                                    }
                                } catch (Exception e) {
                                    log.error("解析模型返回信息出错！" + chunk, e);
                                    handleException(emitter, chunk + ServiceExceptionUtil.getStackTrace(e));
                                    return;
                                }
                            }
                        }
                        //         数据接收完成
                        saveQuestionAndAnswerAndSession(chatVO, builder.toString(), isSimulate);
                        emitter.complete();
                    } catch (IOException e) {
                        log.error("模型对话出错！", e);
                        handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
                    }
                });
        return emitter;
    }

    private void updateAnswerThumbs(Long id, Boolean thumbs) {
        Optional<ChatAnswerModel> optional = chatAnswerRepository.findById(id);
        if (!optional.isPresent()) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "回答不存在！");
        }
        ChatAnswerModel chatAnswerModel = optional.get();
        chatAnswerModel.setThumbsUp(thumbs);
        chatAnswerRepository.save(chatAnswerModel);
    }


    private void handleException(SseEmitter emitter, String errorTrace) {
        try {
            emitter.send(
                    SseEmitter.event()
                            .name("error")
                            .data(
                                    CommonResult.error(
                                            OUTER_SERVER_EXCEPTION.getCode(), CHAT_DEFAULT_ERROR_MESSAGE, errorTrace)));
            emitter.complete();
        } catch (ClientAbortException clientAbortException) {
            log.error("连接关闭失败！", clientAbortException);
        } catch (IOException e) {
            log.error("处理异常", e);
        }
    }

    private boolean checkQuestionSpam(String id, String content) {
        if (StringUtils.isEmpty(content)) {
            return false;
        }
        Boolean antispam = antispamUtil.checkAntispam(id, content);
        return (antispam != null && antispam);
    }

    public boolean checkAnswerSpam(String answer) {
        Boolean aBoolean = antispamUtil.checkAntispam(snowflake.nextIdStr(), answer);
        return aBoolean;
    }

    private void handleSpamResponse(SseEmitter emitter, String message) {
        try {
            emitter.send(SseEmitter.event().name("spam")
                    .data(message));
            emitter.complete();
        } catch (IOException e) {
            log.error("模型对话出错！", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
        }
    }

    private boolean checkQuestionSpam(String question, ChatVO chatVO, boolean isSimulate, SseEmitter emitter) {
        Boolean aBoolean = antispamUtil.checkAntispam(snowflake.nextIdStr(), question);
        if (aBoolean) {
            // 给出默认答案
            String answer = "对不起，这个问题超出了我可以处理的范围。您能否提出其他类型的问题？";
            handleSpamResponse(emitter, answer);
            chatVO.setQuestionId(snowflake.nextId());
            saveQuestionAndAnswerAndSession(chatVO, answer, isSimulate);
        }
        return aBoolean;
    }


    private Long checkRobot(Long robotId) {
        if (null == robotId) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "机器人ID！");
        }
        Optional<RobotModel> optional = robotRepository.findById(robotId);
        if (optional.isPresent()) {
            if (optional.get().getRobotStatus() == CommonStatusEnum.INACTIVE) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INACTIVE_OBJECT, "此机器人未被激活，暂时不能使用！");
            }
            return optional.get().getModelId();
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在！");
        }
    }

    private void buildFromRobot(Long robotId, Set<String> kbIds, List<String> fileIds) {
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotKnowledgeModel.robotId.eq(robotId));
        builder.and(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id));
        // builder.and(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId)); //  不再使用内部关联的方式，由 LEFT JOIN 代替
        // builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED)); //  删除启用状态判断

        // 递归查询出当前登录用户有权访问的所有部门和子部门下的知识库
        List<Long> subDepartIds = departRepository.selectSubById(LoginContextHolder.getLoginUserDepartId());
        // 查询出用户所有公司的根部门ID，因为根部门的知识库是全公司共享
        List<DepartModel> rootParent = departRepository.findByParentIdAndTenantId(0L, LoginContextHolder.getLoginUserTenantId());
        for (DepartModel departModel : rootParent) {
            subDepartIds.add(departModel.getId());
        }
        builder.and(qKnowledgeBaseModel.departmentId.in(subDepartIds));

        // 修改为知识库 LEFT JOIN 知识内容
        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qRobotKnowledgeModel)
                .leftJoin(qKnowledgeBaseModel).on(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .leftJoin(qKnowledgeModel).on(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId))
                .where(builder)
                .fetch();

        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                kbIds.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }
    }

    private void buildFromKnowledgeBase(Long knowledgeBaseId, Set<String> kbIds, List<String> fileIds) {
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeBaseModel.id.eq(knowledgeBaseId));
        builder.and(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId));

        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qKnowledgeBaseModel)
                .leftJoin(qKnowledgeModel).on(qKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .where(qKnowledgeBaseModel.id.eq(knowledgeBaseId))
                .fetch();

        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                kbIds.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }

    }


    private List<Map<String, String>> getTagsFromRobot(Long robotId) {
        Map<String, String> map = new HashMap<>();
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QFileModel qFileModel = QFileModel.fileModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;
        QTagModel qTagModel = QTagModel.tagModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotKnowledgeModel.robotId.eq(robotId));
        builder.and(qKnowledgeModel.kbId.eq(qRobotKnowledgeModel.kbId));
        builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED));
        builder.and(qFileModel.kbId.eq(qRobotKnowledgeModel.kbId));
        builder.and(qFileTagModel.fileId.eq(qFileModel.id));
        builder.and(qTagModel.id.eq(qFileTagModel.tagId));

        List<Tuple> tupleList = queryFactory.select(qTagModel.id, qTagModel.tagName)
                .distinct()
                .from(qTagModel, qRobotKnowledgeModel, qFileModel, qFileTagModel, qKnowledgeModel)
                .where(builder)
                .fetch();
        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            map.put(String.valueOf(tuple.get(qTagModel.id)), tuple.get(qTagModel.tagName));
        }
        List result = new ArrayList();
        result.add(map);
        return result;
    }

    private List<Map<String, String>> getTagsFromKnowledgeBase(Long knowledgeBaseId) {
        Map<String, String> map = new HashMap<>();
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QFileModel qFileModel = QFileModel.fileModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;
        QTagModel qTagModel = QTagModel.tagModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeModel.kbId.eq(knowledgeBaseId));
        builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED));
        builder.and(qKnowledgeModel.fileId.eq(qFileModel.id));
        builder.and(qFileTagModel.fileId.eq(qFileModel.id));
        builder.and(qTagModel.id.eq(qFileTagModel.tagId));

        List<Tuple> tupleList = queryFactory.select(qTagModel.id, qTagModel.tagName)
                .distinct()
                .from(qTagModel, qFileModel, qFileTagModel, qKnowledgeModel)
                .where(builder)
                .fetch();
        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            map.put(String.valueOf(tuple.get(qTagModel.id)), tuple.get(qTagModel.tagName));
        }
        List result = new ArrayList();
        result.add(map);
        return result;
    }

    private List<List<String>> getHistory(Long sessionId, Long curQuestionId, Boolean isSimulate) {
        List<List<String>> result = new ArrayList();
        if (!isSimulate) {

            QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(qChatAnswerModel.sessionId.eq(sessionId));
            builder.and(qChatAnswerModel.content.isNotEmpty());
            List<Tuple> answerTuple = queryFactory.select(qChatAnswerModel.questionId
                            , qChatAnswerModel.content
                            , qChatAnswerModel.questionId)
                    .from(qChatAnswerModel)
                    .where(builder)
                    .orderBy(qChatAnswerModel.createTime.desc())
                    .limit(10)
                    .fetch();
            Map<Long, String> answers = new HashMap();
            Iterator<Tuple> iterator = answerTuple.iterator();
            while (iterator.hasNext()) {
                Tuple tuple = iterator.next();
                answers.put(tuple.get(qChatAnswerModel.questionId), tuple.get(qChatAnswerModel.content));
            }
            QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;
            builder = new BooleanBuilder();
            builder.and(qChatQuestionModel.sessionId.eq(sessionId));
            builder.and(qChatQuestionModel.id.ne(curQuestionId));
            List<Tuple> questionTuple = queryFactory.select(qChatQuestionModel.id, qChatQuestionModel.content)
                    .where(builder)
                    .from(qChatQuestionModel)
                    .orderBy(qChatQuestionModel.createTime.desc())
                    .limit(4)
                    .fetch();
            iterator = questionTuple.iterator();
            while (iterator.hasNext()) {
                Tuple tuple = iterator.next();
//            如果问题没有对应的回答，就不把组历史问答给到大模型
                if (answers.containsKey(tuple.get(qChatQuestionModel.id))) {
                    List list = new ArrayList();
                    list.add(tuple.get(qChatQuestionModel.content));
                    list.add(answers.get(tuple.get(qChatQuestionModel.id)));
                    result.add(list);
                }
            }
        }
        return result;
    }


    /**
     * 保存对话内容并生成历史session
     *
     * @param chatVO
     * @param answer
     */
    public void saveQuestionAndAnswerAndSession(ChatVO chatVO, String answer, Boolean isSimulate) {
        log.info("模型回答：" + answer);
        if (isSimulate) {
            return;
        }
        ChatQuestionModel chatQuestionModel = new ChatQuestionModel();
        chatQuestionModel.setId(chatVO.getQuestionId());
        chatQuestionModel.setContent(chatVO.getQuestion());
        chatQuestionModel.setSessionId(chatVO.getSessionId());
        chatQuestionRepository.save(chatQuestionModel);
        ChatAnswerModel chatAnswerModel;
        Optional<ChatAnswerModel> optionalChatAnswerModel = chatAnswerRepository.findById(chatVO.getAnswerId());
        if (optionalChatAnswerModel.isPresent()) {
//        如果回答能找到，说明是重新回答，不仅需要更新回答的内容，还要把点赞也清空
            chatAnswerModel = optionalChatAnswerModel.get();
            chatAnswerModel.setThumbsUp(null);
        } else {
            chatAnswerModel = new ChatAnswerModel();
            chatAnswerModel.setId(chatVO.getAnswerId());
            chatAnswerModel.setContent(answer);
            chatAnswerModel.setQuestionId(chatVO.getQuestionId());
            chatAnswerModel.setSessionId(chatVO.getSessionId());
        }
        chatAnswerModel.setContent(answer);
        chatAnswerRepository.save(chatAnswerModel);
        Optional<ChatSessionModel> optional = chatSessionRepository.findById(chatVO.getSessionId());
        if (!optional.isPresent()) {
            ChatSessionModel chatSessionModel = new ChatSessionModel();
            chatSessionModel.setId(chatVO.getSessionId());
            chatSessionModel.setRobotId(chatVO.getRobotId());
            chatSessionModel.setTitle(chatVO.getQuestion());
            chatSessionRepository.save(chatSessionModel);
        }
    }
}
