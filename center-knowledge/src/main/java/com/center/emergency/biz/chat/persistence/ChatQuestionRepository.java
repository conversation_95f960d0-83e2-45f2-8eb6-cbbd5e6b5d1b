package com.center.emergency.biz.chat.persistence;


import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChatQuestionRepository extends JoinFetchCapableQueryDslJpaRepository<ChatQuestionModel, Long> {

    List<ChatQuestionModel> findBySessionIdOrderByCreateTimeAsc(Long sessionId);
}
