package com.center.emergency.biz.chat.common.listener;

import com.center.emergency.biz.chat.common.enumeration.ParsedEventType;
import com.center.emergency.biz.chat.common.parser.StreamParser;
import com.center.emergency.biz.chat.common.persitence.ParsedEvent;
import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.service.ChatServiceImpl;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.CountDownLatch;

/**
 * 业务层 Listener：把解析后的事件按业务需求推送前端、落库。
 */
@Slf4j
public class ChatSSEListener extends GenericSSEListener {

    private final ChatServiceImpl service;
    private final ChatVO chatVO;
    private final boolean simulate;
    private final StringBuilder builder;

    public ChatSSEListener(StreamParser parser,
                           CountDownLatch latch,
                           SseEmitter emitter,
                           ChatServiceImpl service,
                           ChatVO chatVO,
                           boolean simulate,
                           StringBuilder sharedBuilder) {
        super(emitter, parser, latch);
        this.service   = service;
        this.chatVO    = chatVO;
        this.simulate  = simulate;
        this.builder   = sharedBuilder;
    }

    /* ------------- 处理业务事件 ------------- */
    @Override
    protected void handleParsedEvent(ParsedEvent ev, EventSource es) throws IOException {
        switch (ev.getEventType()) {
            case MESSAGE:
                if (StringUtils.isBlank(ev.getContent())) break;   // ★ 空串不处理
//                builder.append(ev.getContent());
//                sendEvent("message", ev.getContent());
                sendEvent("message",
                        Collections.singletonMap("content", ev.getContent()));
                break;

            case SPAM:
                sendEvent("spam", ev.getContent());
                complete();                 // 完成并关闭
                es.cancel();
                break;

            case END:
                persistAndFinish();
                break;

            case ERROR:
                sendError(ev.getContent());
                es.cancel();
                break;

            default:
                log.warn("未识别事件类型: {}", ev.getEventType());
        }
    }

    /* ------------- 私有辅助 ------------- */

    /** 正常结束：落库并发送 end 事件 */
    private void persistAndFinish() throws IOException {
        service.saveQuestionAndAnswerAndSession(chatVO, builder.toString(), simulate);
        sendEvent("end", chatVO);           // data 格式保持与旧版一致
        complete();
    }
}
