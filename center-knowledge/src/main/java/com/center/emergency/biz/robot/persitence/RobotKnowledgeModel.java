package com.center.emergency.biz.robot.persitence;

import com.center.framework.db.core.BaseDeleteModel;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@QueryEntity
@Table(
        name = "center_robot_knowledge_bases"
)
public class RobotKnowledgeModel extends BaseTenantModel {

    @Column(name = "robot_id")
    Long robotId;

    @Column(name = "kb_id")
    private Long kbId;

}
