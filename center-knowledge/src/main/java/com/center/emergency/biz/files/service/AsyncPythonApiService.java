package com.center.emergency.biz.files.service;


import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.files.persistence.FileModel;
import com.center.emergency.biz.files.pojo.FileUploadReq;
import com.center.emergency.biz.files.pojo.FileGenerateKnowledgeReq;
import com.center.emergency.biz.knowledge.pojo.QaWithCiteResponse;
import com.center.emergency.biz.knowledge.pojo.KnowledgeNoFileCreateReq;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;

import com.center.emergency.biz.tag.pojo.TagFileRsp;
import com.center.emergency.biz.tag.pojo.TagResp;
import com.center.emergency.biz.tag.pojo.TagUpdateReq;
import com.center.emergency.biz.tag.service.TagServiceImpl;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;

import com.center.framework.common.utils.knowledge.KnowledgeApiTool;
import com.center.framework.common.utils.object.OrikaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 异步调用 Python API 上传文件的服务
 */
@Slf4j
@Service
public class AsyncPythonApiService {

    @Value("${python.api-base-url}")
    private String pythonApiBaseUrl;

    @Value("${python.api-base-dfsurl}")
    private String pythonApiBaseDfsUrl;
    @Resource
    TagServiceImpl tagService;
    @Resource
    KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    Snowflake snowflake;

    @Value("${python.api-url-process}")
    private String processUrl;


    /**
     * 异步调用 Python API 进行文件上传处理
     *
     * @param fileUploadReq       文件上传对象
     */

    @Async
    public void asyncCallPythonUpload(FileUploadReq fileUploadReq) {

        // 根据 kb_id 查询知识库信息
        KnowledgeBaseModel knowledgeBase = knowledgeBaseRepository.findById(fileUploadReq.getKbId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在，AI无法创建知识库"));

        // 获取知识库的 aiFilebId（模型文件库的 ID）
        String aiFilebId = knowledgeBase.getAiFilebId();
        if (aiFilebId == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, "知识库的 aiFilebId 为空，无法调用 API");
        }

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_id", aiFilebId);
        requestBody.put("mode","strong");
        requestBody.put("hdfs_file_paths", Collections.singletonList(
                pythonApiBaseDfsUrl+fileUploadReq.getHdfsPath()
        ));
        requestBody.put("file_id", fileUploadReq.getId().toString());
        List<TagResp> tenantAndSystemTags = tagService.getTenantAndSystemTags(LoginContextHolder.getLoginUserTenantId());
        List<Map<String, String>> tagsToMapListmaps = convertTagsToMapList(tenantAndSystemTags);
        requestBody.put("tag_list", tagsToMapListmaps);

        try {
            // 调用 Python API
            JSONObject responseObj = KnowledgeApiTool.post(pythonApiBaseUrl+"/api/local_doc_qa/upload_hdfs_files", requestBody);

            Map<String, Object> logResponseObj = new LinkedHashMap<>(responseObj);
            if (logResponseObj.containsKey("pdf_text")) {
                logResponseObj.put("pdf_text", "This is a long PDF-base64 content");
            }
            log.info("调用 Python API 返回结果: {}", logResponseObj);

            // 可以根据 responseStr 判断是否有错误并处理

            if (!"200".equals(responseObj.getString("code"))) {
                log.error("Python API 返回失败，错误信息: {}", responseObj.getString("msg"));
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, responseObj.getString("msg"));
            }

        } catch (Exception e) {
            log.error("调用 Python API 失败: {}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "调用 Python API 失败: " + e.getMessage());
        }
    }
    public void updateTagsOnPythonApi(Long fileId, List<TagUpdateReq> tags) {
        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("file_id", fileId.toString());

        // 构建新标签列表
        List<Map<String, String>> tagList = tags.stream()
                .map(tag -> Collections.singletonMap(tag.getId().toString(), tag.getTagName()))
                .collect(Collectors.toList());

        requestBody.put("new_tags", tagList);

        try {
            // 调用 Python API
            JSONObject responseObj = KnowledgeApiTool.post(pythonApiBaseUrl + "/api/local_doc_qa/update_tags_by_file_id", requestBody);
            log.info("AI 更新标签返回结果: {}", responseObj);

            // 检查返回状态
            if (!"200".equals(responseObj.getString("code"))) {
                String errorMsg = "AI更新标签失败，错误信息: " + responseObj.getString("msg");
                log.error(errorMsg);
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                        errorMsg + ", 请求体: " + requestBody.toString()
                );
            }

        } catch (Exception e) {
            String errorMsg = "调用 Python API 更新标签失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                    errorMsg + ", 请求体: " + requestBody.toString(),
                    e
            );
        }
    }


    /**
     * 同步调用算法的文件删除 API
     *
     * @param fileModel 要删除的文件实体
     */
    public void deleteFileOnPythonApi(FileModel fileModel) {
        // 获取知识库的 kb_id
        String kbId = knowledgeBaseRepository.findById(fileModel.getKbId())
                .map(KnowledgeBaseModel::getAiFilebId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_id", kbId);
        requestBody.put("file_ids", Collections.singletonList(fileModel.getId().toString()));

        // 调用 API 删除文件
        try {
            JSONObject response = KnowledgeApiTool.post(pythonApiBaseUrl + "/api/local_doc_qa/delete_files", requestBody);
            log.info("算法 API 删除文件成功: {}", response.getString("msg"));

            // 检查返回状态
            if (!"200".equals(response.getString("code"))) {
                String errorMsg = "算法 API 删除文件失败，错误信息: " + response.getString("msg");
                log.error(errorMsg);
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                        errorMsg + ", 请求体: " + requestBody.toString()
                );
            }

        } catch (Exception e) {
            String errorMsg = "调用算法删除文件 API 失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                    errorMsg + ", 请求体: " + requestBody.toString(),
                    e
            );
        }
    }

    /**
     * 将标签列表转换为 List<Map<String, String>> 格式
     *
     * @param tags 标签列表
     * @return 转换后的 Map 列表
     */
    private List<Map<String, String>> convertTagsToMapList(List<TagResp> tags) {
        List<Map<String, String>> tagList = new ArrayList<>();
        for (int i = 0; i < tags.size(); i++) {
            TagResp tag = tags.get(i);
            Map<String, String> tagMap = Collections.singletonMap(String.valueOf(tag.getId()), tag.getTagName());
            tagList.add(tagMap);
        }
        return tagList;
    }

    /*
    * 根据文件生成知识
    * */
    public void generateKnowledgeOnPythonApi(FileGenerateKnowledgeReq req) {
        // 获取知识库的 kb_id
        String kbId = knowledgeBaseRepository.findById(req.getKbId())
                .map(KnowledgeBaseModel::getAiFaqbId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_id", kbId);
        requestBody.put("file_id", req.getId().toString());
        requestBody.put("pdf_text", req.getPdfText());

        List<TagResp> tagResps = OrikaUtils.convertList(req.getTags(), TagResp.class);
        // 使用现有的 convertTagsToMapList 方法来转换标签
        List<Map<String, String>> tagList = convertTagsToMapList(tagResps);
        requestBody.put("tags", tagList);

        // 调用 API
        try {
            JSONObject responseObj = KnowledgeApiTool.post(
                    pythonApiBaseUrl + "/api/local_doc_qa/generate_qa_with_cite", requestBody);
            log.info("调用 Python API 生成知识返回结果: {}", responseObj);

            // 如果 API 调用返回的状态码不是 200
            if (!"200".equals(responseObj.getString("code"))) {
                String errorMsg = "Python API 生成知识失败，错误信息: " + responseObj.getString("msg");
                log.error(errorMsg);
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                        errorMsg + ", 请求体: " + requestBody.toString()
                );
            }

        } catch (Exception e) {
            String errorMsg = "调用 Python API 生成知识失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                    errorMsg + ", 请求体: " + requestBody.toString(),
                    e
            );
        }
    }


    /**
     * 调用 Python API 更新 QA 对
     *
     * @param req KnowledgeNoFileCreateReq 对象
     */

    public void updateFaqsOnPythonApi(KnowledgeNoFileCreateReq req, List<TagFileRsp> tagFileRsps) {
        // 获取知识库的 kb_id
        String kbId = knowledgeBaseRepository.findById(req.getKbId())
                .map(KnowledgeBaseModel::getAiFaqbId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"
                ));

        // 构建新标签列表
        List<Map<String, String>> tagList = tagFileRsps.stream()
                .map(tag -> Collections.singletonMap(tag.getId().toString(), tag.getTagName()))
                .collect(Collectors.toList());

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_id", kbId);
        requestBody.put("file_id", req.getFileId().toString());
        requestBody.put("faqs", req.getFaqs());
        requestBody.put("tags", tagList);

        // 调用 Python API
        try {
            JSONObject responseObj = KnowledgeApiTool.post(
                    pythonApiBaseUrl + "/api/local_doc_qa/update_faqs", requestBody
            );
            log.info("调用 Python API 更新 QA 对返回结果: {}", responseObj);

            // 检查返回状态
            if (!"200".equals(responseObj.getString("code"))) {
                String errorMsg = "Python API 更新 QA 对失败，错误信息: " + responseObj.getString("msg");
                log.error(errorMsg);
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                        errorMsg + ", 请求体: " + requestBody.toString()
                );
            }
        } catch (Exception e) {
            String errorMsg = "调用 Python API 更新 QA 对失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                    errorMsg + ", 请求体: " + requestBody.toString(),
                    e
            );
        }
    }


    public List<QaWithCiteResponse> getQaWithCite(String fileId) {
        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("file_id", fileId);

        try {
            // 调用 Python API 获取 QA 对
            JSONObject responseObj = KnowledgeApiTool.post(pythonApiBaseUrl + "/api/local_doc_qa/get_qa_with_cite", requestBody);

            Map<String, Object> logResponseObj = new LinkedHashMap<>(responseObj);
            if (logResponseObj.containsKey("data")) {
                logResponseObj.put("data", "这里是很多QA");
            }
            log.info("调用 Python API 获取 QA 对返回结果: {}", logResponseObj);

            // 检查返回状态
            if (!"200".equals(responseObj.getString("code"))) {
                String errorMsg = responseObj.getString("msg");
                log.error("获取 QA 对失败，错误信息: {}", errorMsg);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, errorMsg);
            }

            // 解析响应数据
            List<QaWithCiteResponse> qaList = JSON.parseArray(responseObj.getString("data"), QaWithCiteResponse.class);

            // 处理 contentDetails 字段中的 chunk_id，将其分解并生成 UUID，同时按 score 降序排序
            // 遍历和排序 contentDetails
            for (QaWithCiteResponse qa : qaList) {
                if (qa.getContentDetails() != null) {
                    for (Map<String, Object> detail : qa.getContentDetails()) {
                        if (detail.containsKey("chunk_id")) {
                            String chunkId = (String) detail.get("chunk_id");
                            if (chunkId != null && chunkId.matches("\\d+_\\d+")) {
                                String extractedNumber = chunkId.split("_")[1];
                                detail.put("id", extractedNumber);
                            }
                        }
                        detail.put("uuid", snowflake.nextId());
                    }

                    // 使用 BigDecimal 进行降序排序
                    qa.getContentDetails().sort((detail1, detail2) -> {
                        BigDecimal score1 = (BigDecimal) detail1.getOrDefault("score", BigDecimal.ZERO);
                        BigDecimal score2 = (BigDecimal) detail2.getOrDefault("score", BigDecimal.ZERO);
                        return score2.compareTo(score1); // 降序排序
                    });
                }
            }


            return qaList;

        } catch (Exception e) {
            log.error("调用 Python API 获取 QA 对失败: {}", e.getMessage());
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "获取 QA 对失败: " + e.getMessage());
        }
    }


    public void deleteFaqsOnPythonApi(Long kbId, List<String> fileIds) {
        // 获取知识库的 kb_id
        String kb_id = knowledgeBaseRepository.findById(kbId)
                .map(KnowledgeBaseModel::getAiFaqbId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_id", kb_id);
        requestBody.put("file_ids", fileIds);

        try {
            // 调用 Python API
            JSONObject responseObj = KnowledgeApiTool.post(pythonApiBaseUrl + "/api/local_doc_qa/delete_faqs", requestBody);
            log.info("调用 Python API 删除 FAQ 返回结果: {}", responseObj);

            // 检查返回状态
            if (!"200".equals(responseObj.getString("code"))) {
                log.error("Python API 删除 FAQ 失败，错误信息: {}", responseObj.getString("msg"));
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, responseObj.getString("msg"));
            }

        } catch (Exception e) {
            log.error("调用 Python API 删除 FAQ 失败: {}", e.getMessage());
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "调用 Python API 删除 FAQ 失败: " + e.getMessage());
        }
    }
    @Async
    public void asyncPostJob(Map<String, Object> resultMap) {
        // 调用 Python API
        try {
            KnowledgeApiTool.postJob(processUrl, resultMap);
        } catch (Exception e) {
            log.error("调用 Python API 失败: {}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "调用 Python API 失败: " + e.getCause());
        }
    }

}