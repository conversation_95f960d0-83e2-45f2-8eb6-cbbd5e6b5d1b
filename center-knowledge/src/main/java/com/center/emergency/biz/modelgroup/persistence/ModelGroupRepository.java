package com.center.emergency.biz.modelgroup.persistence;

import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;

@Repository
public interface ModelGroupRepository extends JpaRepository<ModelGroup, Long> {
    boolean existsByGroupName(@NotBlank(message = "模型组名称不能为空") @Length(min = 1, max = 50, message = "模型名称长度必须在1-50字符之间") String groupName);
}
