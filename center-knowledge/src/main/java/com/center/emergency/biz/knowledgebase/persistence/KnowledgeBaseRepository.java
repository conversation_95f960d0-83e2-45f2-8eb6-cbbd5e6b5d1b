package com.center.emergency.biz.knowledgebase.persistence;

import com.center.emergency.biz.knowledgebase.pojo.KnowledgeBaseResp;
import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface KnowledgeBaseRepository extends JoinFetchCapableQueryDslJpaRepository<KnowledgeBaseModel, Long>, JpaSpecificationExecutor<KnowledgeBaseModel> {

    /**
     * 检查同一部门下是否存在相同名称的知识库
     * @param kbName 知识库名称
     * @param departmentId 部门ID
     * @return 是否存在相同名称的知识库
     */
    boolean existsByKbNameAndDepartmentId(String kbName, Long departmentId);


    /**
     * 根据部门ID和租户ID查询知识库
     * @param departmentId 部门ID列表
     * @param tenantId 租户ID
     * @return 知识库列表
     */

    List<KnowledgeBaseModel> findByDepartmentIdAndTenantId(Long departmentId, Long tenantId, Sort sort);



    List<KnowledgeBaseModel> findByDepartmentId(Long departId);

    List<KnowledgeBaseModel> findByKbNameContainingAndIdIn(String keyWords, List<Long> ids);

    /**
     *
     * @param departmentIds
     * * @return 知识库列表
     */
    List<KnowledgeBaseModel> findByDepartmentIdIn(List<Long> departmentIds);
}