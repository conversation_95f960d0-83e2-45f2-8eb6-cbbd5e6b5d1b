package com.center.emergency.biz.chat.common.listener;

import com.center.emergency.biz.chat.common.persitence.ParsedEvent;
import com.center.emergency.biz.chat.common.parser.StreamParser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 协议层通用 SSE 监听器：
 * <pre>
 *   1. 负责将 OkHttp 回调转换为 ParsedEvent
 *   2. 统一管理 complete / timeout / failure
 *   3. 子类重写 {@link #handleParsedEvent(ParsedEvent, EventSource)} 处理业务
 * </pre>
 */
@Slf4j
public class GenericSSEListener extends EventSourceListener {

    protected final SseEmitter emitter;
    protected final StreamParser parser;
    protected final CountDownLatch latch;
    protected final AtomicBoolean completed = new AtomicBoolean(false);

    public GenericSSEListener(SseEmitter emitter,
                              StreamParser parser,
                              CountDownLatch latch) {
        this.emitter = emitter;
        this.parser  = parser;
        this.latch   = latch;

        /* 监听 Spring 端超时 / 完成 */
        emitter.onTimeout(() -> {
            log.warn("SseEmitter timeout, force complete.");
            complete();
            latch.countDown();
        });
        emitter.onCompletion(() -> completed.set(true));
        emitter.onError(ex -> completed.set(true));
    }

    /* ----------------- OkHttp 回调 ----------------- */

    @Override
    public void onOpen(EventSource es, Response res) {
        log.info("SSE connected: {}", res.request().url());
    }

    @Override
    public void onEvent(EventSource es, String id, String eventType, String data) {
        if (completed.get()) return;
        try {
            ParsedEvent event = parser.parse(data, eventType);
            handleParsedEvent(event, es);
        } catch (Exception ex) {
            log.error("解析 SSE 数据异常, raw={}", data, ex);
            sendError("解析失败：" + ex.getMessage());
            es.cancel();
        }
    }

    @Override
    public void onClosed(EventSource es) {
        log.info("SSE closed by server.");
        complete();
        latch.countDown();
    }

    @Override
    public void onFailure(EventSource es, Throwable t, Response res) {
        log.error("SSE failure: {}", t == null ? "unknown" : t.getMessage(), t);
        sendError("SSE 通信失败");
        latch.countDown();
    }

    /* ----------------- 供子类复写 ----------------- */

    /**
     * 子类根据事件类型进行业务处理
     */
    protected void handleParsedEvent(ParsedEvent event, EventSource es) throws IOException {}

    /* ----------------- 公共辅助 ----------------- */

    protected void sendEvent(String name, Object data) {
        if (completed.get()) return;
        try {
            emitter.send(SseEmitter.event().name(name).data(data));
        } catch (IOException io) {
            log.error("发送事件 {} 失败：{}", name, io.getMessage());
            complete();
        }
    }

    protected void sendError(String message) {
        sendEvent("error", message);
        complete();
    }

    protected void complete() {
        if (completed.compareAndSet(false, true)) {
            try { emitter.complete(); } catch (Exception ignore) {}
        }
    }
}
