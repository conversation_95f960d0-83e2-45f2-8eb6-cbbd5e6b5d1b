package com.center.emergency.biz.modelgroup.service;

import com.center.emergency.biz.modelgroup.pojo.*;
import com.center.framework.web.pojo.PageResult;

import javax.validation.constraints.NotBlank;
import java.util.List;

public interface ModelGroupService {
    /**
     * 创建模型组
     *
     * @param req 创建请求参数
     * @return 创建成功后的模型组id
     */
    Long createModelGroup(ModelGroupCreateReq req);

    Long deleteModelGroup(Long id);

    ModelGroupResp updateModelGroup(ModelGroupUpdateReq req);

    PageResult<ModelGroupPageResp> pageModelGroup(ModelGroupPageReq req);

    ModelGroupDetailResp getModelGroup(Long id);

    List<LargeModelBase> listLargeModel(String name);
}
