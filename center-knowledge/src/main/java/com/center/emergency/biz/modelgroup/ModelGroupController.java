package com.center.emergency.biz.modelgroup;


import com.center.emergency.biz.modelgroup.pojo.*;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "模型组管理")
@RequestMapping("/model_group")
@Validated
public class ModelGroupController {

    @Resource
    private ModelGroupService modelGroupService;

    @PostMapping("/create")
    @Operation(summary = "创建模型组")
    @EnumConvertPoint
    public CommonResult<Long> createModelGroup(@RequestBody @Validated ModelGroupCreateReq req) {
        Long modelGroupId = modelGroupService.createModelGroup(req);
        return CommonResult.success(modelGroupId,"创建成功");
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除模型组")
    public CommonResult<Long> deleteModelGroup(@PathVariable("id") Long id) {
        Long modelGroupId = modelGroupService.deleteModelGroup(id);
        return CommonResult.success(modelGroupId,"删除成功");
    }

    @PostMapping("/update")
    @Operation(summary = "更新模型组")
    @EnumConvertPoint
    public CommonResult<ModelGroupResp> updateModelGroup(@RequestBody @Validated ModelGroupUpdateReq req) {
        return CommonResult.success(modelGroupService.updateModelGroup(req));
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询模型组")
    @EnumConvertPoint
    public CommonResult<PageResult<ModelGroupPageResp>> pageModelGroup(ModelGroupPageReq req) {
        return CommonResult.success(modelGroupService.pageModelGroup(req));
    }


    @GetMapping("/get/{id}")
    @Operation(summary = "查询模型组详细信息")
    public CommonResult<ModelGroupDetailResp> getModelGroup(@PathVariable("id") Long id) {
        return CommonResult.success(modelGroupService.getModelGroup(id),"查询成功");
    }

    @GetMapping("/list")
    @Operation(summary = "根据名称查询模型列表")
    public CommonResult<List<LargeModelBase>> listLargeModel(@RequestParam String name) {
        return CommonResult.success(modelGroupService.listLargeModel(name),"查询成功");
    }

}
