package com.center.emergency.biz.chat.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "机器人对话")
public class ChatWithRobotReq {

    @NotBlank(message = "问题不能为空")
    @Schema(description = "问题内容")
    private String question;

    @Schema(description = "机器人ID")
    @NotNull(message = "机器人ID不能为空")
    private Long robotId;

    @Schema(description = "对话SessionID")
    private Long sessionId;
}
