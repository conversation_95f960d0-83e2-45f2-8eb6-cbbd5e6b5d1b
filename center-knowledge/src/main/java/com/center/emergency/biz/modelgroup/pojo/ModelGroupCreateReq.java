package com.center.emergency.biz.modelgroup.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class ModelGroupCreateReq {

    @Schema(description = "模型组名称")
    @NotBlank(message = "模型组名称不能为空")
    @Length(min = 1, max = 50, message = "模型名称长度必须在1-50字符之间")
    private String groupName;

    @Schema(description = "模型组的模型id列表")
    @NotEmpty(message = "模型id列表不能为空")
    private List<Long> normalModelList;

    @Schema(description = "模型组的自适应模型ID列表")
    private List<Long> adaptiveModelList;

    @Schema(description = "模型组深度思考模型ID")
    private Long thinkModelId;
}
