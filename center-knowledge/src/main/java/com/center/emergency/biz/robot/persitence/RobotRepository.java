package com.center.emergency.biz.robot.persitence;


import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RobotRepository extends JoinFetchCapableQueryDslJpaRepository<RobotModel, Long> {
    // 分页查询所有机器人
    boolean existsByRobotNameAndTenantId(String name, Long tenantId);

    List<RobotModel> findByTenantIdAndRobotStatus(Long tenantId, CommonStatusEnum status);

    boolean existsByTenantId(Long tenantId);

    Integer countByModelId(Long modelId);

    List<RobotModel> findByModelIdIn(List<Long> modelIds);

    List<RobotModel> findByModelId(Long modelId);
}
