package com.center.emergency.biz.chat.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;

import java.util.List;

public interface ChatAnswerRepository extends JoinFetchCapableQueryDslJpaRepository<ChatAnswerModel,Long> {

    List<ChatAnswerModel> findByQuestionId(Long questionId);

    List<ChatAnswerModel> findBySessionIdOrderByCreateTimeAsc(Long sessionId);
}
