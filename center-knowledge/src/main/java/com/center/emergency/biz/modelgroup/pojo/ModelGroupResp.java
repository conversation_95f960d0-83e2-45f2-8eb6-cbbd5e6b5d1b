package com.center.emergency.biz.modelgroup.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ModelGroupResp {

    @Schema(description = "模型组id")
    private Long modelGroupId;

    @Schema(description = "模型组名称")
    private String groupName;

    @Schema(description = "模型组的模型id列表")
    private List<Long> normalModelList;

    @Schema(description = "模型组的自适应模型ID列表")
    private List<Long> adaptiveModelList;

    @Schema(description = "模型组深度思考模型ID")
    private Long thinkModelId;
}
