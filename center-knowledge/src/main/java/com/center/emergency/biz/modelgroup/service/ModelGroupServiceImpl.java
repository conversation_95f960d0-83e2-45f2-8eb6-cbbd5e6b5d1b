package com.center.emergency.biz.modelgroup.service;

import cn.hutool.core.lang.Snowflake;
import com.center.emergency.biz.model.persistence.LargeModel;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.biz.modelgroup.persistence.*;
import com.center.emergency.biz.modelgroup.pojo.*;
import com.center.emergency.common.enumeration.ModelRoleEnum;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.*;

@Service
public class ModelGroupServiceImpl implements ModelGroupService {

    @Resource
    Snowflake snowflake;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Autowired
    private ModelGroupRepository modelGroupRepository;

    @Autowired
    private ModelGroupMemberRepository modelGroupMemberRepository;

    @Override
    @Transactional
    public Long createModelGroup(ModelGroupCreateReq req) {
        //1. 存储模型组相关内容
        ModelGroup modelGroup = OrikaUtils.convert(req, ModelGroup.class);
        //1.1 模型组名称判重
        boolean isExist = modelGroupRepository.existsByGroupName(req.getGroupName());
        if (isExist) {
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "模型组名称重复，请更换");
        }
        //1.2 判断 thinkModelId 是否在 normalModelList 中
        if (!(req.getThinkModelId() == null || req.getNormalModelList().contains(req.getThinkModelId()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "深度思考模型必须属于普通模型");
        }

        // 1.3 判断 adaptiveModelList 是否全部在 normalModelList 中
        if (!(CollectionUtils.isEmpty(req.getAdaptiveModelList())
                || req.getNormalModelList().containsAll(req.getAdaptiveModelList()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "自适应模型必须全部属于普通模型");
        }

        Long modelGroupId = snowflake.nextId();
        modelGroup.setId(modelGroupId);
        modelGroupRepository.save(modelGroup);

        // 2. 处理模型组成员
        List<ModelGroupMember> allMembers = new ArrayList<>();

        // 2.1 常规模型成员
        buildMembers(allMembers, req.getNormalModelList(), modelGroupId, ModelRoleEnum.NORMAL);

        // 2.2 自适应模型成员
        buildMembers(allMembers, req.getAdaptiveModelList(), modelGroupId, ModelRoleEnum.ADAPTIVE);

        // 2.3 深度思考模型成员（只有一个）
        if (req.getThinkModelId() != null) {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(modelGroupId);
            member.setLargeModelId(req.getThinkModelId());
            member.setRole(ModelRoleEnum.DEEP_THINK);
            allMembers.add(member);
        }

        // 3. 批量保存所有成员
        if (!allMembers.isEmpty()) {
            modelGroupMemberRepository.saveAll(allMembers);
        }
        return modelGroupId;
    }

    private void buildMembers(List<ModelGroupMember> collector, List<Long> modelIds, Long groupId, ModelRoleEnum role) {
        if (CollectionUtils.isEmpty(modelIds)) return;

        List<ModelGroupMember> members = modelIds.stream().filter(Objects::nonNull).map(modelId -> {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(groupId);
            member.setLargeModelId(modelId);
            member.setRole(role);
            return member;
        }).collect(Collectors.toList());

        collector.addAll(members);
    }

    @Override
    @Transactional
    public Long deleteModelGroup(Long id) {
        // 1. 校验模型组是否存在
        ModelGroup modelGroup = modelGroupRepository.findById(id).orElseThrow(
                () -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "模型组不存在"));

        //TODO 2. 校验是否有助手使用该模型组

        // 3. 删除模型组表记录
        modelGroupRepository.deleteById(id);

        // 4. 删除模型组成员表中的记录
        modelGroupMemberRepository.deleteByModelGroupId(id);

        // 5. 返回删除成功的ID
        return id;
    }

    @Override
    @Transactional
    public ModelGroupResp updateModelGroup(ModelGroupUpdateReq req) {
        Long groupId = req.getModelGroupId();

        // 1. 校验模型组是否存在
        ModelGroup modelGroup = modelGroupRepository.findById(groupId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "模型组不存在"));

        //1.1 模型组名称判重
        if (!modelGroup.getGroupName().equals(req.getGroupName())) {
            boolean isExist = modelGroupRepository.existsByGroupName(req.getGroupName());
            if (isExist) {
                throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "模型组名称重复，请更换");
            }
        }

        //1.1 判断 thinkModelId 是否在 normalModelList 中
        if (!(req.getThinkModelId() == null || req.getNormalModelList().contains(req.getThinkModelId()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "深度思考模型必须属于普通模型");
        }

        // 1.2 判断 adaptiveModelList 是否全部在 normalModelList 中
        if (!(CollectionUtils.isEmpty(req.getAdaptiveModelList())
                || req.getNormalModelList().containsAll(req.getAdaptiveModelList()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "自适应模型必须全部属于普通模型");
        }

        // 2. 更新模型组属性
        modelGroup.setGroupName(req.getGroupName());
        modelGroupRepository.save(modelGroup);

        // 3. 清除原有成员
        modelGroupMemberRepository.deleteByModelGroupId(groupId);

        // 4. 构建新成员列表
        List<ModelGroupMember> allMembers = new ArrayList<>();

        // 4.1 普通模型
        buildMembers(allMembers, req.getNormalModelList(), groupId, ModelRoleEnum.NORMAL);
        // 4.2 自适应模型
        buildMembers(allMembers, req.getAdaptiveModelList(), groupId, ModelRoleEnum.ADAPTIVE);
        // 4.3 深度思考模型（只有一个）
        if (req.getThinkModelId() != null) {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(groupId);
            member.setLargeModelId(req.getThinkModelId());
            member.setRole(ModelRoleEnum.DEEP_THINK);
            allMembers.add(member);
        }

        // 批量保存
        if (!allMembers.isEmpty()) {
            modelGroupMemberRepository.saveAll(allMembers);
        }

        // 5. 返回更新后的模型组响应
        ModelGroupResp resp = OrikaUtils.convert(modelGroup, ModelGroupResp.class);
        // 如果需要把成员列表也塞到 resp 中，可以另外查询并转换
        List<Long> normalIds = req.getNormalModelList();
        List<Long> adaptiveIds = req.getAdaptiveModelList();
        resp.setNormalModelList(normalIds);
        resp.setAdaptiveModelList(adaptiveIds);
        resp.setThinkModelId(req.getThinkModelId());
        return resp;
    }

    @Override
    public PageResult<ModelGroupPageResp> pageModelGroup(ModelGroupPageReq req) {
        // 1. 构建分页条件
        Pageable pageable = PageRequest.of(req.getPageNo() - 1, req.getPageSize());

        // 2. 构建查询条件
        QModelGroup qModelGroup = QModelGroup.modelGroup;
        QModelGroupMember qModelGroupMember = QModelGroupMember.modelGroupMember;
        QLargeModel qLargeModel = QLargeModel.largeModel;
        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(req.getGroupName())) {
            builder.and(qModelGroup.groupName.contains(req.getGroupName()));
        }
        builder.and(qModelGroup.tenantId.eq(LoginContextHolder.getLoginUserTenantId()));

        // 3. 执行查询
        JPQLQuery jpqlQuery = jpaQueryFactory.select((Projections.bean(
                        ModelGroupPageResp.class,
                        qModelGroup.id,
                        qModelGroup.groupName
                )))
                .from(qModelGroup)
                .where(builder)
                .orderBy(qModelGroup.createTime.desc()) // 按 createTime 倒序排列
                .offset(pageable.getOffset()) // 使用 pageable 的偏移量
                .limit(pageable.getPageSize()); // 设置每页记录数

        Long total = jpqlQuery.fetchCount();
        List<ModelGroupPageResp> resultList = jpqlQuery.fetch();

        // 4. 查询该模型组对应的成员模型
        // 4.1 收集所有的模型组ID
        List<Long> groupIDs = new ArrayList<>();
        if (total > 0) {
            groupIDs = resultList.stream().map(ModelGroupPageResp::getId).collect(Collectors.toList());
        }
        // 4.2 批量查询所有的成员及模型名称
        List<LargeModelInfoDTO> largeModelInfoList = jpaQueryFactory.select((Projections.bean(
                        LargeModelInfoDTO.class,
                        qLargeModel.id.as("largeModelId"),
                        qLargeModel.modelDisplayName.as("largeModelDisplayName"),
                        qModelGroupMember.role,
                        qModelGroupMember.modelGroupId
                )))
                .from(qModelGroupMember)
                .join(qLargeModel).on(qModelGroupMember.largeModelId.eq(qLargeModel.id))
                .where(qModelGroupMember.modelGroupId.in(groupIDs))
                .fetch();

        // 4.3 按 modelGroupId 分组
        Map<Long, List<LargeModelInfoDTO>> grouped = largeModelInfoList.stream()
                .collect(Collectors.groupingBy(LargeModelInfoDTO::getModelGroupId));

        // 4.4 将分组后的模型信息填充到分页结果中
        for (ModelGroupPageResp resp : resultList) {
            List<LargeModelInfoDTO> infos = grouped.get(resp.getId());
            resp.setModelList(infos != null ? infos : Collections.emptyList());
        }

        //  构建分页响应并返回
        return PageResult.of(resultList, total);
    }

    @Override
    public ModelGroupDetailResp getModelGroup(Long id) {
        // 1. 查询模型组基本信息
        ModelGroup modelGroup = modelGroupRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "模型组不存在"));

        // 2. 查询该模型组的所有成员
        List<ModelGroupMember> members = modelGroupMemberRepository.findByModelGroupId(id);

        // 3. 收集所有模型 ID
        Set<Long> modelIds = members.stream()
                .map(ModelGroupMember::getLargeModelId)
                .collect(Collectors.toSet());

        // 4. 查询所有大模型id和名称
        QLargeModel qLargeModel = QLargeModel.largeModel;
        List<LargeModelBase> LargeModelInfoList = jpaQueryFactory
                .select((Projections.bean(
                        LargeModelBase.class,
                        qLargeModel.id.as("largeModelId"),
                        qLargeModel.modelDisplayName.as("largeModelDisplayName")
                )))
                .from(qLargeModel)
                .where(qLargeModel.id.in(modelIds))
                .fetch();
        Map<Long, String> modelIdNameMap = LargeModelInfoList
                .stream()
                .collect(Collectors.toMap(
                        LargeModelBase::getLargeModelId,
                        LargeModelBase::getLargeModelDisplayName
                ));


        // 5. 构造返回数据
        List<LargeModelBase> normalModelList = new ArrayList<>();
        List<LargeModelBase> adaptiveModelList = new ArrayList<>();
        Long thinkModelId = null;

        for (ModelGroupMember member : members) {
            Long modelId = member.getLargeModelId();
            String modelDisplayName = modelIdNameMap.get(modelId);
            LargeModelBase info = new LargeModelBase();
            info.setLargeModelId(modelId);
            info.setLargeModelDisplayName(modelDisplayName);

            switch (member.getRole()) {
                case NORMAL:
                    normalModelList.add(info);
                    break;
                case ADAPTIVE:
                    adaptiveModelList.add(info);
                    break;
                case DEEP_THINK:
                    thinkModelId = modelId;
                    break;
            }
        }

        // 6. 封装响应
        ModelGroupDetailResp resp = new ModelGroupDetailResp();
        resp.setModelGroupId(modelGroup.getId());
        resp.setGroupName(modelGroup.getGroupName());
        resp.setNormalModelList(normalModelList);
        resp.setAdaptiveModelList(adaptiveModelList);
        resp.setThinkModelId(thinkModelId);

        return resp;
    }

    @Override
    public List<LargeModelBase> listLargeModel(String name) {
        QLargeModel qLargeModel = QLargeModel.largeModel;
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        // 查询条件：租户模型 或 系统模型
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qLargeModel.tenantId.eq(tenantId).or(qLargeModel.sourceType.eq(SourceTypeEnum.SYSTEM)));
        builder.and(qLargeModel.modelDisplayName.contains(name));

        List<LargeModelBase> modelsList = jpaQueryFactory.select((Projections.bean(
                        LargeModelBase.class,
                        qLargeModel.id.as("largeModelId"),
                        qLargeModel.modelDisplayName.as("largeModelDisplayName")
                ))).
                from(qLargeModel)
                .where(builder)
                .orderBy(qLargeModel.createTime.desc())
                .fetch();

        return OrikaUtils.convertList(modelsList, LargeModelBase.class);
    }
}
