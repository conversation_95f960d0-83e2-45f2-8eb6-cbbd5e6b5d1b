package com.center.emergency.biz.robot.persitence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RobotKBRepository extends JoinFetchCapableQueryDslJpaRepository<RobotKnowledgeModel,Long> {

    void deleteByRobotId(Long robotId);

    // 检查是否有机器人引用该知识库
    boolean existsByKbId(Long kbId);
}
