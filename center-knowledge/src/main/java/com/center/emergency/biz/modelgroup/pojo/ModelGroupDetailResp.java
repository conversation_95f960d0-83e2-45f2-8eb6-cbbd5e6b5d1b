package com.center.emergency.biz.modelgroup.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ModelGroupDetailResp {

    @Schema(description = "模型组id")
    private Long modelGroupId;

    @Schema(description = "模型组名称")
    private String groupName;

    @Schema(description = "模型组的模型id列表")
    private List<LargeModelBase> normalModelList;

    @Schema(description = "模型组的自适应模型ID列表")
    private List<LargeModelBase> adaptiveModelList;

    @Schema(description = "模型组深度思考模型ID")
    private Long thinkModelId;
}
