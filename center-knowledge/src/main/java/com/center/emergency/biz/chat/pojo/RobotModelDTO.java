package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RobotModelDTO {

    private String apiBase;

    private String apiKey;

    private String model;

    private Integer maxToken;

    private Integer inputContextLength;

    private SearchModeEnum searchMode;

    private AnswerStrategyEnum answerMode;

    private BigDecimal scoreThreshold;

    private Float temperature;

    private Float topP;

    private Integer topK;

    private String customPrompt;
}
