package com.center.framework.storage.factory;


import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.storage.hdfs.impl.HdfsFileStorageService;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.framework.storage.interfaces.config.FileStorageConfig;
import com.center.framework.storage.interfaces.enums.FileStorageTypeEnum;
import com.center.framework.storage.minio.impl.MinioFileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;

/**
 * 文件存储服务工厂类，根据存储类型获取对应的存储服务实例。
 */
@Component
@Slf4j
public class FileStorageServiceFactory {

    private final FileStorageService fileStorageService;

    /**
     * 构造方法，注入各存储服务的实现和配置。
     *
     * @param hdfsService  HDFS 存储服务实现
     * @param minioService MinIO 存储服务实现
     * @param config       文件存储配置
     */
    public FileStorageServiceFactory(@Nullable HdfsFileStorageService hdfsService,
                                     @Nullable MinioFileStorageService minioService,
                                     FileStorageConfig config) {
        FileStorageTypeEnum storageType = config.getStorageType();
        switch (storageType) {
            case HDFS:
                this.fileStorageService = hdfsService;
                log.info("已选择 HDFS 作为文件存储方式");
                break;
            case MINIO:
                this.fileStorageService = minioService;
                log.info("已选择 MinIO 作为文件存储方式");
                break;
            default:
                log.error("不支持的存储类型：{}", storageType);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "不支持的存储类型：" + storageType);
        }
    }

    /**
     * 获取当前配置的文件存储服务实例。
     *
     * @return 文件存储服务实例
     */
    public FileStorageService getFileStorageService() {
        return fileStorageService;
    }
}
